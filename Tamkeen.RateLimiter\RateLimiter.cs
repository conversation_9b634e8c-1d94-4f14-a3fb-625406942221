﻿using Serilog;
using System;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Tamkeen.CecheService;
using Tamkeen.RateLimiter.Exception;

namespace Tamkeen.RateLimiter
{

    public class RateLimiter : IRateLimiter
    {
        private readonly ILogger _logger;
        internal readonly ICacheProvider _cashService;
        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

        internal readonly string _name;
        private readonly int _bucketSize;
        private readonly double _leakRatePerMillis;

        public RateLimiter(ILogger logger, ICacheProvider cashService, string name, int bucketSize, double leakRatePerMinute)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _cashService = cashService ?? throw new ArgumentNullException(nameof(cashService));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentException("Name is required", nameof(name));
            if (bucketSize <= 0) throw new ArgumentOutOfRangeException(nameof(bucketSize));
            if (leakRatePerMinute <= 0) throw new ArgumentOutOfRangeException(nameof(leakRatePerMinute));

            _name = name;
            _bucketSize = bucketSize;
            _leakRatePerMillis = leakRatePerMinute / (60.0 * 1000.0);
        }

        public virtual bool Validate(string key)
        {
            return Validate(key, 1);
        }

        public virtual bool Validate(string key, int amount)
        {
            return ValidateAsync(key, amount, CancellationToken.None).GetAwaiter().GetResult();
        }

        public virtual async Task<bool> ValidateAsync(string key, int amount = 1, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException("Key is required", nameof(key));
            if (amount <= 0) throw new ArgumentOutOfRangeException(nameof(amount));

            var correlationId = Guid.NewGuid();
            await _semaphore.WaitAsync(cancellationToken).ConfigureAwait(false);
            try
            {
                var bucket = await GetBucketAsync(key, cancellationToken).ConfigureAwait(false);
                if (bucket.Add(amount))
                {
                    return await SetBucketAsync(key, bucket, cancellationToken).ConfigureAwait(false);
                }

                throw new RateLimitExceededException("Rate limit exceeded");
            }
            catch (RateLimiterException)
            {
                throw;
            }
            catch (System.Exception ex)
            {
                _logger.Error(ex, "Rate limiter failure {CorrelationId}", correlationId);
                throw new RateLimiterException("Rate limiter failure");
            }
            finally
            {
                _semaphore.Release();
            }
        }

        private async Task<bool> SetBucketAsync(string key, LeakyBucket bucket, CancellationToken cancellationToken)
        {
            string serialized = bucket.Serialize();
            int time = (int)Math.Ceiling(_bucketSize / _leakRatePerMillis) / 1000;
            return await _cashService.SetValueAsync(GetBucketName(key), serialized, TimeSpan.FromSeconds(time), cancellationToken).ConfigureAwait(false);
        }

        private async Task<LeakyBucket> GetBucketAsync(string key, CancellationToken cancellationToken)
        {
            try
            {
                var serialized = await _cashService.GetValueAsync(GetBucketName(key), TimeSpan.Zero, cancellationToken).ConfigureAwait(false);
                if (serialized != null)
                {
                    return LeakyBucket.FromSerialized(serialized);
                }
            }
            catch (RateLimiterException)
            {
                _logger.Warning("Invalid bucket data for key {Key}", key);
            }
            catch (System.Exception ex)
            {
                _logger.Warning(ex, "Cache error");
            }

            return new LeakyBucket(_bucketSize, _leakRatePerMillis);
        }

        private string GetBucketName(string key)
        {
            var sanitized = Regex.Replace(key, @"[^a-zA-Z0-9_-]", "_");
            return "leaky_bucket::" + _name + "::" + sanitized;
        }
    }

}
