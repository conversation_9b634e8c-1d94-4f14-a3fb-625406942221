using System.Threading.Tasks;
using Serilog;
using Tamkeen.RateLimiter.Exception;
using Xunit;

namespace Tamkeen.RateLimiter.Tests
{
    public class LockingRateLimiterTests
    {
        private readonly ILogger _logger = new LoggerConfiguration().CreateLogger();

        [Fact]
        public async Task Lock_PreventsConcurrentAccess()
        {
            var cache = new InMemoryCacheProvider();
            var limiter = new LockingRateLimiter(_logger, cache, "test", 10, 600);
            var first = limiter.ValidateAsync("key");
            await Assert.ThrowsAsync<RateLimitExceededException>(() => limiter.ValidateAsync("key"));
            Assert.True(await first);
        }
    }
}
