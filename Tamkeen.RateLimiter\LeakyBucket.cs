﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Text.Json;
using Tamkeen.RateLimiter.Utils;
using Tamkeen.RateLimiter.Exception;

namespace Tamkeen.RateLimiter
{
    internal class LeakyBucket
    {
        private readonly int _bucketSize;
        private readonly double _leakRatePerMillis;
        private int _spaceRemaining;
        private long _lastUpdateTimeMillis;

        public LeakyBucket(int bucketSize, double leakRatePerMillis) : this(bucketSize, leakRatePerMillis, bucketSize, TimeUtils.CurrentTimeMillis())
        {
        }

        private LeakyBucket(int bucketSize, double leakRatePerMillis, int spaceRemaining, long lastUpdateTimeMillis)
        {
            _bucketSize = bucketSize;
            _leakRatePerMillis = leakRatePerMillis;
            _spaceRemaining = spaceRemaining;
            _lastUpdateTimeMillis = lastUpdateTimeMillis;
        }

        public bool Add(int amount)
        {
            _spaceRemaining = GetUpdatedSpaceRemaining();
            _lastUpdateTimeMillis = TimeUtils.CurrentTimeMillis();

            if (_spaceRemaining >= amount)
            {
                _spaceRemaining -= amount;
                return true;
            }

            return false;
        }

        private int GetUpdatedSpaceRemaining()
        {
            long elapsedTime = TimeUtils.CurrentTimeMillis() - _lastUpdateTimeMillis;

            return Math.Min(_bucketSize,
                (int)Math.Floor(_spaceRemaining + (elapsedTime * _leakRatePerMillis)));
        }


        public static LeakyBucket FromSerialized(string serialized)
        {
            try
            {
                var entity = JsonSerializer.Deserialize<LeakyBucketEntity>(serialized, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (entity == null)
                {
                    throw new RateLimiterException("Invalid bucket data");
                }

                return new LeakyBucket(entity.BucketSize, entity.LeakRatePerMillis,
                    entity.SpaceRemaining, entity.LastUpdateTimeMillis);
            }
            catch (JsonException ex)
            {
                throw new RateLimiterException("Failed to deserialize bucket", ex);
            }
        }


        public virtual string Serialize()
        {
            return JsonSerializer.Serialize(new LeakyBucketEntity(_bucketSize, _leakRatePerMillis, _spaceRemaining, _lastUpdateTimeMillis));

        }

        private class LeakyBucketEntity
        {


            public int BucketSize { get; set; }
            public double LeakRatePerMillis { get; set; }


            public int SpaceRemaining { get; set; }


            public long LastUpdateTimeMillis { get; set; }

            public LeakyBucketEntity()
            {
            }

            internal LeakyBucketEntity(int bucketSize, double leakRatePerMillis, int spaceRemaining, long lastUpdateTimeMillis)
            {
                BucketSize = bucketSize;
                LeakRatePerMillis = leakRatePerMillis;
                SpaceRemaining = spaceRemaining;
                LastUpdateTimeMillis = lastUpdateTimeMillis;
            }


        }
    }
}
