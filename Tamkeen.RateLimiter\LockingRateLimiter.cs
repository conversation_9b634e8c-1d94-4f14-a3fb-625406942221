﻿using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.CecheService;
using Tamkeen.RateLimiter.Exception;

namespace Tamkeen.RateLimiter
{
    public class LockingRateLimiter : RateLimiter
    {
        public LockingRateLimiter(ILogger logger, ICacheProvider cashService, string name, int bucketSize, double leakRatePerMillis) : base(logger, cashService, name, bucketSize, leakRatePerMillis)
        {
        }

        public override bool Validate(string key, int amount)
        {
            if (!AcquireLock(key))
            {
                throw new RateLimitExceededException("Locked");
            }

            try
            {
                return base.Validate(key, amount);
            }
            finally
            {
                ReleaseLock(key);
            }
        }

        public override bool Validate(string key)
        {
            return Validate(key, 1);
        }
        private bool ReleaseLock(string key)
        {

            return _cashService.SetLockRelease(GetLockName(key), "L");
        }

        private bool AcquireLock(string key)
        {
            return _cashService.SetWithLock(GetLockName(key), "L", TimeSpan.FromSeconds(10));
        }

        private string GetLockName(string key)
        {
            return "leaky_lock::" + base._name + "::" + key;
        }
    }
}
