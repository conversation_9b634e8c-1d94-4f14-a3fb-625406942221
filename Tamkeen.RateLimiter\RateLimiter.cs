﻿using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tamkeen.CecheService;
using Tamkeen.RateLimiter.Exception;

namespace Tamkeen.RateLimiter
{

    public class RateLimiter
    {
        private readonly ILogger _logger;
        internal readonly ICacheProvider _cashService;

        internal readonly string _name;
        private readonly int _bucketSize;
        private readonly double _leakRatePerMillis;

        public RateLimiter(ILogger logger, ICacheProvider cashService, string name, int bucketSize, double leakRatePerMillis)
        {
            if (logger == null) throw new ArgumentNullException(nameof(logger));
            if (cashService == null) throw new ArgumentNullException(nameof(cashService));

            _logger = logger;
            _cashService = cashService;
            _name = name;
            _bucketSize = bucketSize;
            _leakRatePerMillis = leakRatePerMillis / (60.0 * 1000.0);

        }

        public virtual bool Validate(string key)
        {
            return Validate(key, 1);
        }

        public virtual bool Validate(string key, int amount)
        {
            LeakyBucket bucket = GetBucket(key);

            if (bucket.Add(amount))
            {
                return SetBucket(key, bucket);
            }
            else
            {
                throw new RateLimitExceededException($"Exceeded: {key}, {amount}");
            }
        }

        private bool SetBucket(string key, LeakyBucket bucket)
        {
            string serialized = bucket.Serialize();

            int time = (int)Math.Ceiling(_bucketSize / _leakRatePerMillis) / 1000;
            return _cashService.SetValue(GetBucketName(key), serialized, TimeSpan.FromSeconds(time));

        }

        private LeakyBucket GetBucket(string key)
        {

            try
            {
                var serialized = _cashService.GetValue(GetBucketName(key), TimeSpan.Zero);
                if (serialized != null)
                {
                    return LeakyBucket.FromSerialized(serialized);
                }
            }
            catch (StackOverflowException exception)
            {
                _logger.Warning(exception, "Redis error");
            }
            catch (System.Exception e)
            {
                _logger.Warning(e, "Deserialization error");
            }

            return new LeakyBucket(_bucketSize, _leakRatePerMillis);
        }


        private string GetBucketName(string key)
        {
            return "leaky_bucket::" + _name + "::" + key;
        }
    }

}
