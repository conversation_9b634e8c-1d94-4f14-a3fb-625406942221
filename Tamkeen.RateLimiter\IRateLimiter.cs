namespace Tamkeen.RateLimiter
{
    using System.Threading;
    using System.Threading.Tasks;

    /// <summary>
    /// Represents a leaky bucket rate limiter.
    /// </summary>
    public interface IRateLimiter
    {
        /// <summary>
        /// Validates that the specified key is within the configured rate limit asynchronously.
        /// </summary>
        /// <param name="key">Unique identifier for the rate limit bucket.</param>
        /// <param name="amount">The cost of the current request.</param>
        /// <param name="cancellationToken"><PERSON><PERSON> used to cancel the operation.</param>
        /// <returns><c>true</c> if the request is allowed; otherwise throws <see cref="RateLimitExceededException"/>.</returns>
        Task<bool> ValidateAsync(string key, int amount = 1, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates synchronously that the key is within the rate limit.
        /// </summary>
        bool Validate(string key, int amount = 1);

        /// <summary>
        /// Validates synchronously with a default cost of one.
        /// </summary>
        bool Validate(string key);
    }
}
