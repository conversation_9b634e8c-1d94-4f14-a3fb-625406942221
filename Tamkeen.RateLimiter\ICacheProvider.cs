namespace Tamkeen.CecheService
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;

    public interface ICacheProvider
    {
        // Legacy synchronous API
        string? GetValue(string key, TimeSpan expiry);
        bool SetValue(string key, string value, TimeSpan expiry);
        bool SetWithLock(string key, string value, TimeSpan expiry);
        bool SetLockRelease(string key, string value);

        // Async API
        Task<string?> GetValueAsync(string key, TimeSpan expiry, CancellationToken cancellationToken = default);
        Task<bool> SetValueAsync(string key, string value, TimeSpan expiry, CancellationToken cancellationToken = default);
        Task<bool> SetWithLockAsync(string key, string value, TimeSpan expiry, CancellationToken cancellationToken = default);
        Task<bool> SetLockReleaseAsync(string key, string value, CancellationToken cancellationToken = default);
    }
}
