using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using Tamkeen.CecheService;

namespace Tamkeen.RateLimiter.Tests
{
    internal class InMemoryCacheProvider : ICacheProvider
    {
        private readonly ConcurrentDictionary<string, string> _store = new();
        private readonly ConcurrentDictionary<string, object> _locks = new();

        public string? GetValue(string key, TimeSpan expiry)
        {
            _store.TryGetValue(key, out var value);
            return value;
        }

        public bool SetValue(string key, string value, TimeSpan expiry)
        {
            _store[key] = value;
            return true;
        }

        public bool SetWithLock(string key, string value, TimeSpan expiry)
        {
            return _locks.TryAdd(key, new object());
        }

        public bool SetLockRelease(string key, string value)
        {
            return _locks.TryRemove(key, out _);
        }

        public Task<string?> GetValueAsync(string key, TimeSpan expiry, CancellationToken cancellationToken = default)
            => Task.FromResult(GetValue(key, expiry));

        public async Task<bool> SetValueAsync(string key, string value, TimeSpan expiry, CancellationToken cancellationToken = default)
        {
            // simulate async latency
            await Task.Delay(5, cancellationToken);
            return SetValue(key, value, expiry);
        }

        public Task<bool> SetWithLockAsync(string key, string value, TimeSpan expiry, CancellationToken cancellationToken = default)
            => Task.FromResult(SetWithLock(key, value, expiry));

        public Task<bool> SetLockReleaseAsync(string key, string value, CancellationToken cancellationToken = default)
            => Task.FromResult(SetLockRelease(key, value));
    }
}
