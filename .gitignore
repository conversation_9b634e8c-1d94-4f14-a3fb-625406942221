# Ignore NuGet packages folder
*.nuget/
**/packages/*

# Ignore .NET Core artifacts
bin/
obj/
*.dll
*.exe
*.pdb
*.userprefs

# Ignore Visual Studio Code settings
.vscode/

# Ignore project-specific files
*.sln
*.csproj.user
*.xproj
*.lock.json

# Ignore build results
[Dd]ebug/
[Rr]elease/
[Dd]ocker/

# Ignore Visual Studio files
*.vs/
*.suo
*.ntvs*

# Ignore Rider files
.idea/

# Ignore ReSharper files
_ReSharper*/

# Ignore node_modules folder
node_modules/

# Ignore logs and caches
logs/
*.log
*.cache

# Ignore OS generated files
.DS_Store
Thumbs.db

# Ignore user-specific files
*.user
*.rsuser
*.userosscache
*.sln.docstates
*.userprefs

# Ignore generated files by some tools
generated/
