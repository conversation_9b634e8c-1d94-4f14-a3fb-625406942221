# Tamkeen.RateLimiter.Core

An asynchronous leaky bucket rate limiter for .NET 8.

## Installation

```
dotnet add package Tamkeen.RateLimiter.Core
```

## Usage

```
var cache = new InMemoryCacheProvider();
var logger = new LoggerConfiguration().CreateLogger();
IRateLimiter limiter = new RateLimiter(logger, cache, "api", bucketSize:10, leakRatePerMinute:600);
await limiter.ValidateAsync("user-key");
```

## Migration

The library now uses asynchronous APIs. Existing synchronous calls are still available but internally call the async implementations. Replace `Validate` with `ValidateAsync` and supply `CancellationToken` where appropriate.

## Architecture

The implementation follows the [leaky bucket](https://en.wikipedia.org/wiki/Leaky_bucket) algorithm where requests are added to a bucket that leaks at a constant rate.
