﻿using Serilog;
using System;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Tamkeen.CecheService;
using Tamkeen.RateLimiter.Exception;

namespace Tamkeen.RateLimiter
{
    public class LockingRateLimiter : RateLimiter
    {
        public LockingRateLimiter(ILogger logger, ICacheProvider cashService, string name, int bucketSize, double leakRatePerMillis) : base(logger, cashService, name, bucketSize, leakRatePerMillis)
        {
        }

        public override bool Validate(string key, int amount)
        {
            return ValidateAsync(key, amount, CancellationToken.None).GetAwaiter().GetResult();
        }

        public override bool Validate(string key)
        {
            return Validate(key, 1);
        }

        public override async Task<bool> ValidateAsync(string key, int amount = 1, CancellationToken cancellationToken = default)
        {
            if (!await AcquireLockAsync(key, cancellationToken).ConfigureAwait(false))
            {
                throw new RateLimitExceededException("Locked");
            }

            try
            {
                return await base.ValidateAsync(key, amount, cancellationToken).ConfigureAwait(false);
            }
            finally
            {
                await ReleaseLockAsync(key, cancellationToken).ConfigureAwait(false);
            }
        }

        private Task<bool> ReleaseLockAsync(string key, CancellationToken cancellationToken)
        {
            return _cashService.SetLockReleaseAsync(GetLockName(key), "L", cancellationToken);
        }

        private Task<bool> AcquireLockAsync(string key, CancellationToken cancellationToken)
        {
            return _cashService.SetWithLockAsync(GetLockName(key), "L", TimeSpan.FromSeconds(10), cancellationToken);
        }

        private string GetLockName(string key)
        {
            var sanitized = Regex.Replace(key, @"[^a-zA-Z0-9_-]", "_");
            return "leaky_lock::" + base._name + "::" + sanitized;
        }
    }
}
