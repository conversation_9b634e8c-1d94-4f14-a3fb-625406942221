using System.Threading.Tasks;
using Serilog;
using Tamkeen.RateLimiter.Exception;
using Xunit;

namespace Tamkeen.RateLimiter.Tests
{
    public class RateLimiterTests
    {
        private readonly ILogger _logger = new LoggerConfiguration().CreateLogger();

        [Fact]
        public async Task ValidateAsync_AllowsWithinLimits()
        {
            var cache = new InMemoryCacheProvider();
            var limiter = new RateLimiter(_logger, cache, "test", 10, 600);
            var result = await limiter.ValidateAsync("key", 5);
            Assert.True(result);
        }

        [Fact]
        public async Task ValidateAsync_ThrowsWhenExceeded()
        {
            var cache = new InMemoryCacheProvider();
            var limiter = new RateLimiter(_logger, cache, "test", 5, 60);
            await limiter.ValidateAsync("key", 5);
            await Assert.ThrowsAsync<RateLimitExceededException>(() => limiter.ValidateAsync("key", 1));
        }

        [Fact]
        public async Task Concurrency_IsThreadSafe()
        {
            var cache = new InMemoryCacheProvider();
            var limiter = new RateLimiter(_logger, cache, "test", 10, 600);
            var tasks = new Task[10];
            for (int i = 0; i < 10; i++)
            {
                tasks[i] = limiter.ValidateAsync("key" + i);
            }
            await Task.WhenAll(tasks);
        }
    }
}
